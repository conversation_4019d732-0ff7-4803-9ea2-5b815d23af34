# 原始EDV评估函数
def EDV(S, G, p):
    """
    原始影响力评估 (Expected Diffusion Value)
    Args:
        S: 种子节点集合
        G: GEN_GRAPH对象
        p: 传播概率
    Returns:
        float: 估计的影响力值
    """
    S = set(S)
    adj_dict = G.neighbors
    NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S
    influence_sum = 0
    for node in NS_1:
        num_connections = len(set(adj_dict[node]) & S)
        influence_sum += 1 - (1 - p) ** num_connections
    return len(S) + influence_sum

# 原始LIE二跳评估函数
def LIE_two_hop(S, G, p):
    """
    原始二跳LIE影响力评估
    Args:
        S: 种子节点集合
        G: GEN_GRAPH对象
        p: 传播概率
    Returns:
        float: 估计的影响力值
    """
    S = set(S)
    adj_dict = G.neighbors
    neighbor_1st = set()
    for u in S:
        neighbor_1st.update(set(adj_dict[u]) - S)
    neighbor_2nd = set()
    for v in neighbor_1st:
        neighbor_2nd.update(set(adj_dict[v]) - S - neighbor_1st)
    # 一跳
    v_link_count = np.array([len(set(adj_dict[v]) & S) for v in neighbor_1st], dtype=np.float64)
    temp_sum = np.sum(1 - (1 - p) ** v_link_count)
    # 二跳
    num2 = sum(len(set(adj_dict[ss]) & neighbor_1st) for ss in neighbor_2nd)
    temp_sum_2 = (temp_sum * num2 * p) / len(neighbor_1st) if neighbor_1st else 0
    return len(S) + temp_sum + temp_sum_2
from ast import Set
from re import S
import networkx as nx
import numpy as np
import time
import os
import pandas as pd


class GEN_GRAPH:
    """Network graph generator and neighbor cache container
    
    Attributes:
        network_path (str): 网络数据文件路径（包含节点关系的文本文件）
        nx_G (nx.Graph): NetworkX图对象实例
        neighbors (dict): 节点邻居字典缓存{节点: [直接邻居列表]}
    """
    
    def __init__(self, network_path: str) -> None:
        """初始化图生成器并预生成邻居缓存
        
        Args:
            network_path: 网络数据文件路径（需符合np.loadtxt格式要求）
            
        Raises:
            FileNotFoundError: 当指定路径不存在时
            ValueError: 当文件格式不符合要求时
        """
        self.network_path = network_path
        self.nx_G = self.gen_graph()
        self.neighbors = {node: list(self.nx_G.neighbors(node)) for node in self.nx_G.nodes()}

    def gen_graph(self) -> nx.Graph:
        """从边列表文件构建无向图（支持标准边列表格式）
        
        文件格式要求：
        - 首行为表头（将被自动跳过）
        - 每行包含两个整数节点ID（从第0列和第1列读取）
        
        Returns:
            生成的NetworkX无向图对象
            
        Raises:
            FileNotFoundError: 当文件路径不存在时
            ValueError: 当数据列不足或无法转换为整数时
            RuntimeError: 当图构建过程中出现未知错误时
        """
        try:
            G = nx.Graph()
            # 使用numpy加速边数据加载（跳过首行表头）
            edges_data = np.loadtxt(
                self.network_path,
                skiprows=1,    # 忽略标题行
                usecols=[0, 1] # 仅读取前两列作为边
            )
            # 将浮点数据转换为整数节点ID（适用于非负整数ID）
            edges = [(int(u), int(v)) for u, v in edges_data]
            G.add_edges_from(edges)
            return G
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Network file not found: {self.network_path}") from e
        except ValueError as e:
            raise ValueError(f"Invalid data format in {self.network_path}") from e
        except Exception as e:
            raise RuntimeError(f"Graph construction failed: {str(e)}") from e



def optimized_EDV(graph, S, p):
    """
    优化后的影响力评估 (Expected Diffusion Value)
    
    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率
        
    Returns:
        float: 估计的影响力值
    """
    # 预生成邻接字典（将邻居存储为集合）
    adj_dict = graph.neighbors
    S = set(S)
    
    # 计算一阶邻居 (NS_1)，直接使用集合操作
    NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S
    
    # 快速计算每个邻居的连接数
    influence_sum = 0
    for node in NS_1:
        num_connections = len(set(adj_dict[node]) & S)
        influence_sum += 1 - (1 - p) ** num_connections
    
    return len(S) + influence_sum





def IC_vec(g, seed, p, mc=1000):
    """向量化实现的独立级联模型模拟
    
    Args:
        g: NetworkX图对象
        seed: 种子节点集合
        p: 传播概率
        mc: 蒙特卡洛模拟次数
        
    Returns:
        float: 平均影响力扩散范围
    """
    # 预处理节点到索引的映射和邻居列表
    node_list = list(g.nodes())
    node_to_index = {node: idx for idx, node in enumerate(node_list)}
    n = len(node_list)
    
    # 预存每个节点的邻居索引数组（向量化预生成）
    preprocessed_neighbors = [
        np.array([node_to_index[n] for n in g.neighbors(node)], dtype=np.int32)
        for node in node_list
    ]
    
    # 转换种子节点为索引
    seed_indices = np.array([node_to_index[n] for n in seed], dtype=np.int32)
    
    # 预分配内存空间
    influence = np.empty(mc, dtype=np.int32)
    
    # 批量生成随机数（优化随机数生成效率）
    rand_pool = np.random.rand(mc, n*5)  # 预生成随机数池（5倍冗余）
    pool_ptr = 0
    
    for i in range(mc):
        active = np.zeros(n, dtype=np.bool_)
        active[seed_indices] = True
        newly_active = active.copy()
        
        while np.any(newly_active):
            # 向量化收集激活节点的所有邻居
            current_active = np.flatnonzero(newly_active)
            neighbors = np.concatenate([preprocessed_neighbors[idx] for idx in current_active])
            
            if neighbors.size == 0:
                break
            
            # 生成与neighbors长度匹配的随机数
            rand_values = np.random.rand(len(neighbors))  # 直接生成所需长度的随机数
            
            # 向量化筛选激活节点
            activated = neighbors[rand_values < p]
            unique_activated = np.unique(activated)
            
            # 更新激活状态
            is_new = ~active[unique_activated]
            newly_active_nodes = unique_activated[is_new]
            
            active[newly_active_nodes] = True
            newly_active.fill(False)
            newly_active[newly_active_nodes] = True
        
        influence[i] = active.sum()
    
    return np.mean(influence)

# 在向量化计算部分使用更高精度的数据类型
def vectorized_newLIE_two_hop_v2(s_set, G, p):
    """
    基于条件概率修正的LIE二跳影响力估计（支持nx.Graph和向量化运算，IC模型均匀概率）
    Args:
        s_set (set): 种子节点集合
        G (nx.Graph): 网络图对象
        p (float): 传播概率 (统一概率)
    Returns:
        float: 影响力估计值
    """
    s_set = set(s_set)
    # 节点到邻居集合的映射（假设G为nx.Graph，adj_dict[node]为可迭代对象）
    adj_dict = G.adj if hasattr(G, 'adj') else G.neighbors

    # 一跳邻居
    neighbor_1st = set()
    for u in s_set:
        neighbor_1st.update(set(adj_dict[u]) - s_set)
    # 二跳邻居（非种子、非一跳）
    neighbor_2nd = set()
    for v in neighbor_1st:
        neighbor_2nd.update(set(adj_dict[v]) - s_set - neighbor_1st)

    # 计算一跳激活概率P_v
    # 对每个一跳邻居v，统计与s_set有多少条边，P_v = 1 - (1-p)^num_links
    v_list = list(neighbor_1st)
    v_link_count = np.array([len(set(adj_dict[v]) & s_set) for v in v_list], dtype=np.float64)
    P_v = 1 - np.power(1-p, v_link_count)  # 一跳激活概率向量

    v_idx_map = {v: idx for idx, v in enumerate(v_list)}  # 便于后续索引

    # 计算二跳激活概率P_w（条件概率联合补集）
    P_w = []
    for w in neighbor_2nd:
        v_parents = list(set(adj_dict[w]) & neighbor_1st)  # 所有一跳父节点
        if not v_parents:
            continue
        # 分别查找每个v的P_v
        pvs = np.array([P_v[v_idx_map[v]] for v in v_parents])
        # 二跳传播概率p（同为p），即P_v * p
        pvps = pvs * p
        # P(w未被任一v激活) = prod(1 - P_v*p)
        prod_term = np.prod(1 - pvps)
        P_w.append(1 - prod_term)
    P_w = np.array(P_w) if P_w else np.array([])

    # 总影响力：|S| + 一跳激活概率和 + 二跳激活概率和
    result = len(s_set) + np.sum(P_v) + np.sum(P_w)
    return result



def neumann_influence(S, G, p, max_hop=1):
    """
    EDV/LIE风格的 Neumann 局部级数近似（跳数可控）
    Args:
        S: 种子节点集合
        G: 网络图对象
        p: 传播概率
        max_hop: 最大跳数（默认3即可）
    Returns:
        float: 估计的影响力值
    """
    S = set(S)
    adj_dict = G.adj if hasattr(G, 'adj') else G.neighbors
    influence = set(S)  # 激活节点
    last_layer = set(S)
    act_prob = {v: 1.0 for v in S}  # 已激活节点概率（种子为1）
    # 保存每个节点被激活的概率（非种子初始化为0）
    node_prob = {v: 1.0 if v in S else 0.0 for v in G.nodes()}

    for step in range(1, max_hop+1):
        # 新一层
        next_layer = set()
        layer_probs = dict()
        for v in G.nodes():
            if v in influence:
                continue  # 已激活
            # 父节点集合
            parents = set(adj_dict[v]) & last_layer
            if not parents:
                continue
            # 这些父节点的激活概率
            p_parents = [act_prob[u] for u in parents]
            # 父节点各自的传播到v概率
            q_list = [1 - (1 - p) ** 1 for _ in parents]  # 只考虑单条边
            # 父节点激活v的概率（多父概率联合补集）
            q_total = 1.0
            for q, p_u in zip(q_list, p_parents):
                q_total *= (1 - q * p_u)
            prob = 1 - q_total
            if prob > 1e-10:
                next_layer.add(v)
                layer_probs[v] = prob
        # 更新激活概率
        act_prob = layer_probs
        influence.update(next_layer)
        for v in layer_probs:
            node_prob[v] = layer_probs[v]
        last_layer = next_layer
    # 影响力总和：所有激活概率的和
    return sum(node_prob.values())

def BP_influence(S, G, p, max_hop=1):
    """
    BP近似影响力估计（全节点递推）
    Args:
        S: 种子节点集合
        G: 网络X图对象
        p: 传播概率
        max_iter: 最大递推轮数
    Returns:
        float: 估计影响力值
    """
    S = set(S)
    nodes = list(G.nodes())
    # 初始化激活概率
    P = {v: 1.0 if v in S else 0.0 for v in nodes}

    for _ in range(max_hop):
        new_P = P.copy()
        for v in nodes:
            if v in S:
                continue
            parents = list(G.neighbors(v))
            prob_not = 1.0
            for u in parents:
                prob_not *= 1 - p * P[u]
            new_P[v] = 1 - prob_not
        P = new_P
    return sum(P.values())

def optimal_BP_influence(G, S, p, max_hop=3):
    n = G.number_of_nodes()
    nodes = list(G.nodes())
    node_idx = {v: i for i, v in enumerate(nodes)}
    # 稀疏邻接矩阵
    A = nx.to_scipy_sparse_array(G, nodelist=nodes, format='csr')
    P = np.array([1.0 if v in S else 0.0 for v in nodes])
    for _ in range(max_hop):
        new_P = P.copy()
        for i in range(n):
            if nodes[i] in S:
                continue
            # 用 indptr/indices 正确取邻居索引
            neighbors = A.indices[A.indptr[i]:A.indptr[i+1]]
            if len(neighbors) == 0:
                continue
            prob_not = np.prod(1 - p * P[neighbors])
            new_P[i] = 1 - prob_not
        P = new_P
    return np.sum(P)

def main():
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    # network_path = "D:\\VS\\code\\networks\\netscience.txt"
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    # network_path = "D:\\VS\\code\\networks\\pgp.txt"
    # network_path = "D:\\VS\\code\\networks\\CA-GrQc.txt"

    # network_path = "D:\\VS\\code\\networks\\CA-HepTh.txt"
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    # network_path = "D:\\VS\\code\\networks\\deezer.txt"
    # 测试集列表，可自由增删
    test_networks = [
        "D:\\VS\\code\\networks\\netscience.txt",
        "D:\\VS\\code\\networks\\email.txt",
        "D:\\VS\\code\\networks\\blog.txt",
        "D:\\VS\\code\\networks\\pgp.txt",
        "D:\\VS\\code\\networks\\CA-HepTh.txt",
        "D:\\VS\\code\\networks\\NetHEHT.txt",
        # "D:\\VS\\code\\networks\\deezer.txt"
    ]

    k = 50
    # 创建保存结果的文件夹
    result_dir = os.path.join(os.path.dirname(__file__), "evaluate_result")
    os.makedirs(result_dir, exist_ok=True)

    all_results = []
    for network_path in test_networks:
        print(f"\n================ 测试网络: {network_path} ================")
        try:
            G = GEN_GRAPH(network_path)
        except Exception as e:
            print(f"读取网络失败: {e}")
            continue
        degree_centrality = nx.degree_centrality(G.nx_G)
        sorted_nodes = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)
        top_k_nodes = [node for node, _ in sorted_nodes[:k]]

        for p in [0.01, 0.05, 0.1]:
            times = {}
            # 计时并计算各方法
            t0 = time.time(); result_edv = EDV(top_k_nodes, G, p); times['EDV'] = time.time() - t0
            t0 = time.time(); result_lie = LIE_two_hop(top_k_nodes, G, p); times['原始LIE'] = time.time() - t0
            t0 = time.time(); result_lie_v2 = vectorized_newLIE_two_hop_v2(top_k_nodes, G.nx_G, p); times['修正后LIE'] = time.time() - t0
            t0 = time.time(); result_neumann = neumann_influence(top_k_nodes, G.nx_G, p, max_hop=10); times['Neumann级数'] = time.time() - t0
            t0 = time.time(); result_bp = BP_influence(top_k_nodes, G.nx_G, p, max_hop=10); times['BP近似'] = time.time() - t0
            t0 = time.time(); result_opt_bp = optimal_BP_influence(G.nx_G, top_k_nodes, p, max_hop=10); times['稀疏BP近似'] = time.time() - t0
            t0 = time.time(); result_ic = IC_vec(G.nx_G, top_k_nodes, p, mc=10000); times['IC模型模拟'] = time.time() - t0

            def err_and_type(est, ic):
                err = (est - ic) / ic * 100
                if abs(err) < 1e-6:
                    return "误差: 0.00% (与IC基本一致)"
                elif err > 0:
                    return f"误差: {abs(err):.2f}% (高估)"
                else:
                    return f"误差: {abs(err):.2f}% (低估)"

            # 终端输出所有结果和时间
            print(f"\n--- 传播概率 p={p} ---")
            print(f"EDV: {result_edv:.4f} (用时: {times['EDV']:.4f}s)")
            print(f"原始LIE: {result_lie:.4f} (用时: {times['原始LIE']:.4f}s)")
            print(f"修正后LIE: {result_lie_v2:.4f} (用时: {times['修正后LIE']:.4f}s)")
            print(f"Neumann级数: {result_neumann:.4f} (用时: {times['Neumann级数']:.4f}s)")
            print(f"BP近似: {result_bp:.4f} (用时: {times['BP近似']:.4f}s)")
            print(f"稀疏BP近似: {result_opt_bp:.4f} (用时: {times['稀疏BP近似']:.4f}s)")
            print(f"IC模型模拟 (10000次): {result_ic:.4f} (用时: {times['IC模型模拟']:.4f}s)")

            if result_ic > 0:
                all_results.append({
                    "网络": os.path.basename(network_path),
                    "传播概率": p,
                    "EDV": result_edv,
                    "原始LIE": result_lie,
                    "修正后LIE": result_lie_v2,
                    "Neumann级数": result_neumann,
                    "BP近似": result_bp,
                    "稀疏BP近似": result_opt_bp,
                    "IC模型模拟": result_ic,
                    "EDV相对误差": err_and_type(result_edv, result_ic),
                    "原始LIE相对误差": err_and_type(result_lie, result_ic),
                    "修正后LIE相对误差": err_and_type(result_lie_v2, result_ic),
                    "Neumann级数相对误差": err_and_type(result_neumann, result_ic),
                    "BP近似相对误差": err_and_type(result_bp, result_ic),
                    "稀疏BP近似相对误差": err_and_type(result_opt_bp, result_ic),
                    "EDV用时(s)": times['EDV'],
                    "原始LIE用时(s)": times['原始LIE'],
                    "修正后LIE用时(s)": times['修正后LIE'],
                    "Neumann级数用时(s)": times['Neumann级数'],
                    "BP近似用时(s)": times['BP近似'],
                    "稀疏BP近似用时(s)": times['稀疏BP近似'],
                    "IC模型模拟用时(s)": times['IC模型模拟']
                })

    # 保存为Excel
    df = pd.DataFrame(all_results)
    excel_path = os.path.join(result_dir, "评估结果.xlsx")
    df.to_excel(excel_path, index=False)
    print(f"所有评估结果已保存到: {excel_path}")

if __name__ == "__main__":
    main()