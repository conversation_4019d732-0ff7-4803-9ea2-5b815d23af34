import networkx as nx
import numpy as np
import os
import pandas as pd


class GEN_GRAPH:
    """Network graph generator and neighbor cache container
    
    Attributes:
        network_path (str): 网络数据文件路径（包含节点关系的文本文件）
        nx_G (nx.Graph): NetworkX图对象实例
        neighbors (dict): 节点邻居字典缓存{节点: [直接邻居列表]}
    """
    
    def __init__(self, network_path: str) -> None:
        """初始化图生成器并预生成邻居缓存
        
        Args:
            network_path: 网络数据文件路径（需符合np.loadtxt格式要求）
            
        Raises:
            FileNotFoundError: 当指定路径不存在时
            ValueError: 当文件格式不符合要求时
        """
        self.network_path = network_path
        self.nx_G = self.gen_graph()
        self.neighbors = {node: list(self.nx_G.neighbors(node)) for node in self.nx_G.nodes()}

    def gen_graph(self) -> nx.Graph:
        """从边列表文件构建无向图（支持标准边列表格式）
        
        文件格式要求：
        - 首行为表头（将被自动跳过）
        - 每行包含两个整数节点ID（从第0列和第1列读取）
        
        Returns:
            生成的NetworkX无向图对象
            
        Raises:
            FileNotFoundError: 当文件路径不存在时
            ValueError: 当数据列不足或无法转换为整数时
            RuntimeError: 当图构建过程中出现未知错误时
        """
        try:
            G = nx.Graph()
            # 使用numpy加速边数据加载（跳过首行表头）
            edges_data = np.loadtxt(
                self.network_path,
                skiprows=1,    # 忽略标题行
                usecols=[0, 1] # 仅读取前两列作为边
            )
            # 将浮点数据转换为整数节点ID（适用于非负整数ID）
            edges = [(int(u), int(v)) for u, v in edges_data]
            G.add_edges_from(edges)
            return G
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Network file not found: {self.network_path}") from e
        except ValueError as e:
            raise ValueError(f"Invalid data format in {self.network_path}") from e
        except Exception as e:
            raise RuntimeError(f"Graph construction failed: {str(e)}") from e


def BP_influence(S, G, p, max_hop=1):
    """
    BP近似影响力估计（全节点递推）
    Args:
        S: 种子节点集合
        G: 网络X图对象
        p: 传播概率
        max_hop: 最大递推轮数
    Returns:
        float: 估计影响力值
    """
    S = set(S)
    nodes = list(G.nodes())
    # 初始化激活概率
    P = {v: 1.0 if v in S else 0.0 for v in nodes}

    for _ in range(max_hop):
        new_P = P.copy()
        for v in nodes:
            if v in S:
                continue
            parents = list(G.neighbors(v))
            prob_not = 1.0
            for u in parents:
                prob_not *= 1 - p * P[u]
            new_P[v] = 1 - prob_not
        P = new_P
    return sum(P.values())


def IC_vec(g, seed, p, mc=1000):
    """向量化实现的独立级联模型模拟

    Args:
        g: NetworkX图对象
        seed: 种子节点集合
        p: 传播概率
        mc: 蒙特卡洛模拟次数

    Returns:
        float: 平均影响力扩散范围
    """
    # 预处理节点到索引的映射和邻居列表
    node_list = list(g.nodes())
    node_to_index = {node: idx for idx, node in enumerate(node_list)}
    n = len(node_list)

    # 预存每个节点的邻居索引数组（向量化预生成）
    preprocessed_neighbors = [
        np.array([node_to_index[n] for n in g.neighbors(node)], dtype=np.int32)
        for node in node_list
    ]

    # 转换种子节点为索引
    seed_indices = np.array([node_to_index[n] for n in seed], dtype=np.int32)

    # 预分配内存空间
    influence = np.empty(mc, dtype=np.int32)

    for i in range(mc):
        active = np.zeros(n, dtype=np.bool_)
        active[seed_indices] = True
        newly_active = active.copy()

        while np.any(newly_active):
            # 向量化收集激活节点的所有邻居
            current_active = np.flatnonzero(newly_active)
            neighbors = np.concatenate([preprocessed_neighbors[idx] for idx in current_active])

            if neighbors.size == 0:
                break

            # 生成与neighbors长度匹配的随机数
            rand_values = np.random.rand(len(neighbors))

            # 向量化筛选激活节点
            activated = neighbors[rand_values < p]
            unique_activated = np.unique(activated)

            # 更新激活状态
            is_new = ~active[unique_activated]
            newly_active_nodes = unique_activated[is_new]

            active[newly_active_nodes] = True
            newly_active.fill(False)
            newly_active[newly_active_nodes] = True

        influence[i] = active.sum()

    return np.mean(influence)


def main():
    # 测试集列表，可自由增删
    test_networks = [
        "D:\\VS\\code\\networks\\netscience.txt",
        "D:\\VS\\code\\networks\\email.txt",
        "D:\\VS\\code\\networks\\blog.txt",
        "D:\\VS\\code\\networks\\pgp.txt",
        "D:\\VS\\code\\networks\\CA-HepTh.txt",
        "D:\\VS\\code\\networks\\NetHEHT.txt",
    ]

    k = 50
    max_hops = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]  # 设定的max_hop值
    
    # 创建保存结果的文件夹
    result_dir = os.path.join(os.path.dirname(__file__), "BP_result")
    os.makedirs(result_dir, exist_ok=True)

    for network_path in test_networks:
        print(f"\n================ 测试网络: {network_path} ================")
        try:
            G = GEN_GRAPH(network_path)
        except Exception as e:
            print(f"读取网络失败: {e}")
            continue
            
        # 获取网络名称（不含路径和扩展名）
        network_name = os.path.splitext(os.path.basename(network_path))[0]
        
        degree_centrality = nx.degree_centrality(G.nx_G)
        sorted_nodes = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)
        top_k_nodes = [node for node, _ in sorted_nodes[:k]]

        all_results = []
        
        for p in [0.01, 0.05, 0.1]:
            # 先计算IC模型作为基准
            print(f"\n--- 传播概率 p={p} ---")
            print("计算IC模型基准...")
            result_ic = IC_vec(G.nx_G, top_k_nodes, p, mc=10000)
            print(f"IC模型模拟: {result_ic:.4f}")

            for max_hop in max_hops:
                print(f"\n--- max_hop={max_hop} ---")

                # 计算BP方法
                result_bp = BP_influence(top_k_nodes, G.nx_G, p, max_hop=max_hop)

                # 计算误差
                if result_ic > 0:
                    error_bp = (result_bp - result_ic) / result_ic * 100
                    error_type = "高估" if error_bp > 0 else "低估"
                    error_str = f"{abs(error_bp):.2f}% ({error_type})"
                else:
                    error_str = "无法计算"

                print(f"BP近似: {result_bp:.4f}")
                print(f"相对IC误差: {error_str}")

                all_results.append({
                    "网络": network_name,
                    "传播概率": p,
                    "max_hop": max_hop,
                    "BP近似": result_bp,
                    "IC模型": result_ic,
                    "相对误差(%)": f"{error_bp:.2f}" if result_ic > 0 else "N/A",
                    "误差类型": error_type if result_ic > 0 else "N/A"
                })

        # 保存为Excel，文件名包含网络名称
        df = pd.DataFrame(all_results)
        excel_path = os.path.join(result_dir, f"BP_{network_name}_结果.xlsx")
        df.to_excel(excel_path, index=False)
        print(f"BP评估结果已保存到: {excel_path}")


if __name__ == "__main__":
    main()
