# BP_test.py 修改说明

## 修改内容

1. **保留功能**：
   - 只保留了BP影响力计算相关的函数
   - `BP_influence()`: 标准BP近似影响力估计
   - `optimal_BP_influence()`: 优化的稀疏矩阵BP近似影响力估计
   - `GEN_GRAPH`: 网络图生成器类

2. **删除功能**：
   - 删除了EDV评估函数
   - 删除了LIE二跳评估函数
   - 删除了IC模型模拟函数
   - 删除了Neumann级数近似函数
   - 删除了其他非BP相关的评估方法

3. **参数设置**：
   - max_hop设定为：[1, 3, 5, 10]
   - 传播概率p：[0.01, 0.05, 0.1]
   - 种子节点数k：50（基于度中心性选择）

4. **输出结果**：
   - 结果保存在`BP_result`文件夹中
   - 每个网络生成一个独立的Excel文件
   - 文件命名格式：`BP_{网络名称}_结果.xlsx`

## 结果文件结构

每个Excel文件包含以下列：
- 网络：网络名称
- 传播概率：p值（0.01, 0.05, 0.1）
- max_hop：最大跳数（1, 3, 5, 10）
- BP近似：标准BP算法结果
- 稀疏BP近似：优化BP算法结果
- BP近似用时(s)：标准BP算法运行时间
- 稀疏BP近似用时(s)：优化BP算法运行时间

## 测试网络

程序测试了以下6个网络：
1. netscience.txt
2. email.txt
3. blog.txt
4. pgp.txt
5. CA-HepTh.txt
6. NetHEHT.txt

## 运行方式

```bash
python BP_test.py
```

程序会自动：
1. 创建`BP_result`文件夹
2. 对每个网络进行BP影响力计算
3. 保存结果到对应的Excel文件中
4. 在控制台显示实时进度和结果
